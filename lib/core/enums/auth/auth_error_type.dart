/// Enum representing different types of authentication errors
enum AuthErrorType {
  emailAlreadyInUse,
  invalidEmail,
  userNotFound,
  wrongPassword,
  weakPassword,
  userDisabled,
  tooManyRequests,
  networkError,
  unknown,
  cancelled,
  invalidCredential,
  emailNotVerified,
  accountExistsWithDifferentCredential,
}

/// Extension to provide user-friendly error messages
extension AuthErrorTypeExtension on AuthErrorType {
  String get message {
    switch (this) {
      case AuthErrorType.emailAlreadyInUse:
        return 'An account with this email already exists. Please try logging in instead.';
      case AuthErrorType.invalidEmail:
        return 'Please enter a valid email address.';
      case AuthErrorType.userNotFound:
        return 'No account found with this email. Please check your email or sign up.';
      case AuthErrorType.wrongPassword:
        return 'Incorrect password. Please try again.';
      case AuthErrorType.weakPassword:
        return 'Password is too weak. Please use at least 6 characters.';
      case AuthErrorType.userDisabled:
        return 'This account has been disabled. Please contact support.';
      case AuthErrorType.tooManyRequests:
        return 'Too many failed attempts. Please try again later.';
      case AuthErrorType.networkError:
        return 'Network error. Please check your internet connection and try again.';
      case AuthErrorType.cancelled:
        return 'Operation was cancelled.';
      case AuthErrorType.invalidCredential:
        return 'Invalid credentials provided. Please try again.';
      case AuthErrorType.emailNotVerified:
        return 'Please verify your email address before continuing.';
      case AuthErrorType.accountExistsWithDifferentCredential:
        return 'An account with this email exists with a different sign-in method.';
      case AuthErrorType.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Returns a short title for the error
  String get title {
    switch (this) {
      case AuthErrorType.emailAlreadyInUse:
        return 'Email Already Exists';
      case AuthErrorType.invalidEmail:
        return 'Invalid Email';
      case AuthErrorType.userNotFound:
        return 'User Not Found';
      case AuthErrorType.wrongPassword:
        return 'Incorrect Password';
      case AuthErrorType.weakPassword:
        return 'Weak Password';
      case AuthErrorType.userDisabled:
        return 'Account Disabled';
      case AuthErrorType.tooManyRequests:
        return 'Too Many Attempts';
      case AuthErrorType.networkError:
        return 'Network Error';
      case AuthErrorType.cancelled:
        return 'Cancelled';
      case AuthErrorType.invalidCredential:
        return 'Invalid Credentials';
      case AuthErrorType.emailNotVerified:
        return 'Email Not Verified';
      case AuthErrorType.accountExistsWithDifferentCredential:
        return 'Account Exists';
      case AuthErrorType.unknown:
        return 'Error';
    }
  }

  /// Returns whether this error should be retryable
  bool get isRetryable {
    switch (this) {
      case AuthErrorType.networkError:
      case AuthErrorType.unknown:
        return true;
      case AuthErrorType.emailAlreadyInUse:
      case AuthErrorType.invalidEmail:
      case AuthErrorType.userNotFound:
      case AuthErrorType.wrongPassword:
      case AuthErrorType.weakPassword:
      case AuthErrorType.userDisabled:
      case AuthErrorType.tooManyRequests:
      case AuthErrorType.cancelled:
      case AuthErrorType.invalidCredential:
      case AuthErrorType.emailNotVerified:
      case AuthErrorType.accountExistsWithDifferentCredential:
        return false;
    }
  }
}
