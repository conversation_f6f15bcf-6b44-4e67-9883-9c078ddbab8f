import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';

enum AuthStatus { unauthenticated, authenticated, loading }

/// Extension to provide additional functionality for AuthStatus
extension AuthStatusExtension on AuthStatus {
  /// Returns a human-readable label for the auth status
  String get label {
    switch (this) {
      case AuthStatus.unauthenticated:
        return 'Not Authenticated';
      case AuthStatus.authenticated:
        return 'Authenticated';
      case AuthStatus.loading:
        return 'Loading';
    }
  }

  /// Returns the color associated with the auth status
  Color get color {
    switch (this) {
      case AuthStatus.unauthenticated:
        return AppColors.errorLight;
      case AuthStatus.authenticated:
        return AppColors.successLight;
      case AuthStatus.loading:
        return AppColors.warningLight;
    }
  }

  /// Returns whether the user can access protected routes
  bool get canAccessProtectedRoutes {
    switch (this) {
      case AuthStatus.authenticated:
        return true;
      case AuthStatus.unauthenticated:
      case AuthStatus.loading:
        return false;
    }
  }

  /// Returns whether auth screens should be shown
  bool get shouldShowAuthScreens {
    switch (this) {
      case AuthStatus.unauthenticated:
        return true;
      case AuthStatus.authenticated:
      case AuthStatus.loading:
        return false;
    }
  }
}
