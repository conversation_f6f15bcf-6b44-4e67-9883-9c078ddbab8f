import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/user_model.dart';
import '../models/auth_exception.dart';
import '../enums/auth/auth_status.dart';
import '../services/auth_repository.dart';

/// Logger for auth providers
final _logger = Logger();

/// Provider for the auth repository instance
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final repository = AuthRepository();
  repository.initialize();
  return repository;
});

/// Provider for authentication status stream
final authStatusProvider = StreamProvider<AuthStatus>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return repository.authStatusStream;
});

/// Provider for current user stream
final currentUserProvider = StreamProvider<UserModel?>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return repository.userStream;
});

/// Provider for current user ID (replaces mock user ID)
final currentUserIdProvider = Provider<String?>((ref) {
  final userAsync = ref.watch(currentUserProvider);
  return userAsync.when(
    data: (user) => user?.id,
    loading: () => null,
    error: (_, __) => null,
  );
});

/// Provider for checking if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authStatusAsync = ref.watch(authStatusProvider);
  return authStatusAsync.when(
    data: (status) => status == AuthStatus.authenticated,
    loading: () => false,
    error: (_, __) => false,
  );
});

/// Provider for checking if auth is loading
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authStatusAsync = ref.watch(authStatusProvider);
  return authStatusAsync.when(
    data: (status) => status == AuthStatus.loading,
    loading: () => true,
    error: (_, __) => false,
  );
});

/// Provider for sign up operation
final signUpProvider = StateNotifierProvider<SignUpNotifier, AsyncValue<UserModel?>>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return SignUpNotifier(repository);
});

/// Provider for sign in operation
final signInProvider = StateNotifierProvider<SignInNotifier, AsyncValue<UserModel?>>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return SignInNotifier(repository);
});

/// Provider for sign out operation
final signOutProvider = StateNotifierProvider<SignOutNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return SignOutNotifier(repository);
});

/// Provider for password reset operation
final passwordResetProvider = StateNotifierProvider<PasswordResetNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return PasswordResetNotifier(repository);
});

/// Sign up state notifier
class SignUpNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final AuthRepository _repository;

  SignUpNotifier(this._repository) : super(const AsyncValue.data(null));

  /// Sign up with email and password
  Future<void> signUp({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();
    
    state = await AsyncValue.guard(() async {
      _logger.i('Starting sign up process');
      
      final user = await _repository.signUp(
        email: email,
        password: password,
      );
      
      _logger.i('Sign up completed successfully');
      return user;
    });
  }

  /// Reset state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Sign in state notifier
class SignInNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final AuthRepository _repository;

  SignInNotifier(this._repository) : super(const AsyncValue.data(null));

  /// Sign in with email and password
  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();
    
    state = await AsyncValue.guard(() async {
      _logger.i('Starting sign in process');
      
      final user = await _repository.signIn(
        email: email,
        password: password,
      );
      
      _logger.i('Sign in completed successfully');
      return user;
    });
  }

  /// Reset state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Sign out state notifier
class SignOutNotifier extends StateNotifier<AsyncValue<void>> {
  final AuthRepository _repository;

  SignOutNotifier(this._repository) : super(const AsyncValue.data(null));

  /// Sign out current user
  Future<void> signOut() async {
    state = const AsyncValue.loading();
    
    state = await AsyncValue.guard(() async {
      _logger.i('Starting sign out process');
      
      await _repository.signOut();
      
      _logger.i('Sign out completed successfully');
    });
  }

  /// Reset state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Password reset state notifier
class PasswordResetNotifier extends StateNotifier<AsyncValue<void>> {
  final AuthRepository _repository;

  PasswordResetNotifier(this._repository) : super(const AsyncValue.data(null));

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    state = const AsyncValue.loading();
    
    state = await AsyncValue.guard(() async {
      _logger.i('Sending password reset email');
      
      await _repository.sendPasswordResetEmail(email: email);
      
      _logger.i('Password reset email sent successfully');
    });
  }

  /// Reset state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for email verification operation
final emailVerificationProvider = StateNotifierProvider<EmailVerificationNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return EmailVerificationNotifier(repository);
});

/// Email verification state notifier
class EmailVerificationNotifier extends StateNotifier<AsyncValue<void>> {
  final AuthRepository _repository;

  EmailVerificationNotifier(this._repository) : super(const AsyncValue.data(null));

  /// Send email verification
  Future<void> sendEmailVerification() async {
    state = const AsyncValue.loading();
    
    state = await AsyncValue.guard(() async {
      _logger.i('Sending email verification');
      
      await _repository.sendEmailVerification();
      
      _logger.i('Email verification sent successfully');
    });
  }

  /// Reset state
  void reset() {
    state = const AsyncValue.data(null);
  }
}
