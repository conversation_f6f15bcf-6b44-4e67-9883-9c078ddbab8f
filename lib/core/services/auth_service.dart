import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';

import '../models/auth_exception.dart';
import '../models/user_model.dart';

/// Service class for handling Firebase Authentication operations
class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final Logger _logger = Logger();

  /// Stream of authentication state changes
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Stream of user changes (includes profile updates)
  static Stream<User?> get userChanges => _auth.userChanges();

  /// Current authenticated user
  static User? get currentUser => _auth.currentUser;

  /// Current user as UserModel
  static UserModel? get currentUserModel {
    final user = currentUser;
    if (user == null) return null;
    return UserModel.fromFirebaseUser(user);
  }

  /// Sign up with email and password
  static Future<UserModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign up user with email: $email');
      
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        _logger.e('Sign up failed: User is null after creation');
        throw AuthException.unknown();
      }

      final userModel = UserModel.fromFirebaseUser(credential.user!);
      _logger.i('User signed up successfully: ${userModel.id}');
      
      return userModel;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign up: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseCode(e.code, e);
    } catch (e) {
      _logger.e('Unexpected error during sign up: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Sign in with email and password
  static Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign in user with email: $email');
      
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        _logger.e('Sign in failed: User is null after authentication');
        throw AuthException.unknown();
      }

      final userModel = UserModel.fromFirebaseUser(credential.user!);
      _logger.i('User signed in successfully: ${userModel.id}');
      
      return userModel;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign in: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseCode(e.code, e);
    } catch (e) {
      _logger.e('Unexpected error during sign in: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Sign out the current user
  static Future<void> signOut() async {
    try {
      final userId = currentUser?.uid;
      _logger.i('Attempting to sign out user: $userId');
      
      await _auth.signOut();
      
      _logger.i('User signed out successfully: $userId');
    } catch (e) {
      _logger.e('Error during sign out: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Send password reset email
  static Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      _logger.i('Sending password reset email to: $email');
      
      await _auth.sendPasswordResetEmail(email: email);
      
      _logger.i('Password reset email sent successfully to: $email');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error sending password reset: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseCode(e.code, e);
    } catch (e) {
      _logger.e('Unexpected error sending password reset: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Send email verification to current user
  static Future<void> sendEmailVerification() async {
    try {
      final user = currentUser;
      if (user == null) {
        _logger.e('Cannot send email verification: No user signed in');
        throw AuthException.unknown();
      }

      _logger.i('Sending email verification to: ${user.email}');
      
      await user.sendEmailVerification();
      
      _logger.i('Email verification sent successfully to: ${user.email}');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error sending email verification: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseCode(e.code, e);
    } catch (e) {
      _logger.e('Unexpected error sending email verification: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Reload current user to get updated information
  static Future<void> reloadUser() async {
    try {
      final user = currentUser;
      if (user == null) {
        _logger.w('Cannot reload user: No user signed in');
        return;
      }

      _logger.d('Reloading user data for: ${user.uid}');
      await user.reload();
      _logger.d('User data reloaded successfully');
    } catch (e) {
      _logger.e('Error reloading user: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Update user display name
  static Future<void> updateDisplayName(String displayName) async {
    try {
      final user = currentUser;
      if (user == null) {
        _logger.e('Cannot update display name: No user signed in');
        throw AuthException.unknown();
      }

      _logger.i('Updating display name for user: ${user.uid}');
      
      await user.updateDisplayName(displayName);
      
      _logger.i('Display name updated successfully');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error updating display name: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseCode(e.code, e);
    } catch (e) {
      _logger.e('Unexpected error updating display name: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Delete current user account
  static Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user == null) {
        _logger.e('Cannot delete account: No user signed in');
        throw AuthException.unknown();
      }

      final userId = user.uid;
      _logger.i('Deleting account for user: $userId');
      
      await user.delete();
      
      _logger.i('Account deleted successfully: $userId');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error deleting account: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseCode(e.code, e);
    } catch (e) {
      _logger.e('Unexpected error deleting account: $e');
      throw AuthException.unknown(e);
    }
  }
}
