import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';

import '../models/auth_exception.dart';
import '../models/user_model.dart';
import '../enums/auth/auth_status.dart';
import 'auth_service.dart';

/// Repository for managing authentication operations and state
class AuthRepository {
  static final AuthRepository _instance = AuthRepository._internal();
  factory AuthRepository() => _instance;
  AuthRepository._internal();

  final Logger _logger = Logger();

  /// Stream controller for authentication status
  final StreamController<AuthStatus> _authStatusController = 
      StreamController<AuthStatus>.broadcast();

  /// Stream controller for current user
  final StreamController<UserModel?> _userController = 
      StreamController<UserModel?>.broadcast();

  /// Stream of authentication status changes
  Stream<AuthStatus> get authStatusStream => _authStatusController.stream;

  /// Stream of user changes
  Stream<UserModel?> get userStream => _userController.stream;

  /// Current authentication status
  AuthStatus _currentAuthStatus = AuthStatus.loading;
  AuthStatus get currentAuthStatus => _currentAuthStatus;

  /// Current user
  UserModel? _currentUser;
  UserModel? get currentUser => _currentUser;

  /// Initialize the repository and listen to auth state changes
  void initialize() {
    _logger.i('Initializing AuthRepository');
    
    // Listen to Firebase auth state changes
    AuthService.authStateChanges.listen(
      (User? firebaseUser) {
        _handleAuthStateChange(firebaseUser);
      },
      onError: (error) {
        _logger.e('Error in auth state stream: $error');
        _updateAuthStatus(AuthStatus.unauthenticated);
        _updateUser(null);
      },
    );

    // Set initial state based on current user
    final currentFirebaseUser = AuthService.currentUser;
    _handleAuthStateChange(currentFirebaseUser);
  }

  /// Handle Firebase auth state changes
  void _handleAuthStateChange(User? firebaseUser) {
    if (firebaseUser != null) {
      _logger.i('User authenticated: ${firebaseUser.uid}');
      final userModel = UserModel.fromFirebaseUser(firebaseUser);
      _updateAuthStatus(AuthStatus.authenticated);
      _updateUser(userModel);
    } else {
      _logger.i('User not authenticated');
      _updateAuthStatus(AuthStatus.unauthenticated);
      _updateUser(null);
    }
  }

  /// Update authentication status and notify listeners
  void _updateAuthStatus(AuthStatus status) {
    if (_currentAuthStatus != status) {
      _currentAuthStatus = status;
      _authStatusController.add(status);
      _logger.d('Auth status updated: $status');
    }
  }

  /// Update current user and notify listeners
  void _updateUser(UserModel? user) {
    _currentUser = user;
    _userController.add(user);
    _logger.d('User updated: ${user?.id ?? 'null'}');
  }

  /// Sign up with email and password
  Future<UserModel> signUp({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Starting sign up process for email: $email');
      _updateAuthStatus(AuthStatus.loading);

      final user = await AuthService.signUpWithEmailAndPassword(
        email: email,
        password: password,
      );

      _logger.i('Sign up successful for user: ${user.id}');
      return user;
    } on AuthException catch (e) {
      _logger.e('Auth error during sign up: ${e.message}');
      _updateAuthStatus(AuthStatus.unauthenticated);
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error during sign up: $e');
      _updateAuthStatus(AuthStatus.unauthenticated);
      throw AuthException.unknown(e);
    }
  }

  /// Sign in with email and password
  Future<UserModel> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Starting sign in process for email: $email');
      _updateAuthStatus(AuthStatus.loading);

      final user = await AuthService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _logger.i('Sign in successful for user: ${user.id}');
      return user;
    } on AuthException catch (e) {
      _logger.e('Auth error during sign in: ${e.message}');
      _updateAuthStatus(AuthStatus.unauthenticated);
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error during sign in: $e');
      _updateAuthStatus(AuthStatus.unauthenticated);
      throw AuthException.unknown(e);
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      _logger.i('Starting sign out process');
      _updateAuthStatus(AuthStatus.loading);

      await AuthService.signOut();
      
      _logger.i('Sign out successful');
    } on AuthException catch (e) {
      _logger.e('Auth error during sign out: ${e.message}');
      // Even if sign out fails, update status to unauthenticated
      _updateAuthStatus(AuthStatus.unauthenticated);
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error during sign out: $e');
      _updateAuthStatus(AuthStatus.unauthenticated);
      throw AuthException.unknown(e);
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      _logger.i('Sending password reset email to: $email');
      
      await AuthService.sendPasswordResetEmail(email: email);
      
      _logger.i('Password reset email sent successfully');
    } on AuthException catch (e) {
      _logger.e('Auth error sending password reset: ${e.message}');
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error sending password reset: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      _logger.i('Sending email verification');
      
      await AuthService.sendEmailVerification();
      
      _logger.i('Email verification sent successfully');
    } on AuthException catch (e) {
      _logger.e('Auth error sending email verification: ${e.message}');
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error sending email verification: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Reload current user data
  Future<void> reloadUser() async {
    try {
      _logger.d('Reloading user data');
      
      await AuthService.reloadUser();
      
      // Update user model with fresh data
      final firebaseUser = AuthService.currentUser;
      if (firebaseUser != null) {
        final userModel = UserModel.fromFirebaseUser(firebaseUser);
        _updateUser(userModel);
      }
      
      _logger.d('User data reloaded successfully');
    } on AuthException catch (e) {
      _logger.e('Auth error reloading user: ${e.message}');
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error reloading user: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Update user display name
  Future<void> updateDisplayName(String displayName) async {
    try {
      _logger.i('Updating display name');
      
      await AuthService.updateDisplayName(displayName);
      
      // Reload user to get updated data
      await reloadUser();
      
      _logger.i('Display name updated successfully');
    } on AuthException catch (e) {
      _logger.e('Auth error updating display name: ${e.message}');
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error updating display name: $e');
      throw AuthException.unknown(e);
    }
  }

  /// Delete current user account
  Future<void> deleteAccount() async {
    try {
      _logger.i('Deleting user account');
      _updateAuthStatus(AuthStatus.loading);
      
      await AuthService.deleteAccount();
      
      _logger.i('Account deleted successfully');
    } on AuthException catch (e) {
      _logger.e('Auth error deleting account: ${e.message}');
      _updateAuthStatus(AuthStatus.unauthenticated);
      rethrow;
    } catch (e) {
      _logger.e('Unexpected error deleting account: $e');
      _updateAuthStatus(AuthStatus.unauthenticated);
      throw AuthException.unknown(e);
    }
  }

  /// Dispose resources
  void dispose() {
    _logger.d('Disposing AuthRepository');
    _authStatusController.close();
    _userController.close();
  }
}
