import '../enums/auth/auth_error_type.dart';

/// Custom exception class for authentication errors
class AuthException implements Exception {
  final AuthErrorType type;
  final String message;
  final String? code;
  final dynamic originalException;

  const AuthException({
    required this.type,
    required this.message,
    this.code,
    this.originalException,
  });

  /// Creates an AuthException from a Firebase Auth error code
  factory AuthException.fromFirebaseCode(String code, [dynamic originalException]) {
    AuthErrorType type;
    String message;

    switch (code) {
      case 'email-already-in-use':
        type = AuthErrorType.emailAlreadyInUse;
        message = type.message;
        break;
      case 'invalid-email':
        type = AuthErrorType.invalidEmail;
        message = type.message;
        break;
      case 'user-not-found':
        type = AuthErrorType.userNotFound;
        message = type.message;
        break;
      case 'wrong-password':
        type = AuthErrorType.wrongPassword;
        message = type.message;
        break;
      case 'weak-password':
        type = AuthErrorType.weakPassword;
        message = type.message;
        break;
      case 'user-disabled':
        type = AuthErrorType.userDisabled;
        message = type.message;
        break;
      case 'too-many-requests':
        type = AuthErrorType.tooManyRequests;
        message = type.message;
        break;
      case 'network-request-failed':
        type = AuthErrorType.networkError;
        message = type.message;
        break;
      case 'operation-not-allowed':
        type = AuthErrorType.unknown;
        message = 'This operation is not allowed. Please contact support.';
        break;
      case 'invalid-credential':
        type = AuthErrorType.invalidCredential;
        message = type.message;
        break;
      case 'account-exists-with-different-credential':
        type = AuthErrorType.accountExistsWithDifferentCredential;
        message = type.message;
        break;
      default:
        type = AuthErrorType.unknown;
        message = 'An unexpected error occurred. Please try again.';
    }

    return AuthException(
      type: type,
      message: message,
      code: code,
      originalException: originalException,
    );
  }

  /// Creates an AuthException for network errors
  factory AuthException.networkError([dynamic originalException]) {
    return AuthException(
      type: AuthErrorType.networkError,
      message: AuthErrorType.networkError.message,
      code: 'network-error',
      originalException: originalException,
    );
  }

  /// Creates an AuthException for cancelled operations
  factory AuthException.cancelled([dynamic originalException]) {
    return AuthException(
      type: AuthErrorType.cancelled,
      message: AuthErrorType.cancelled.message,
      code: 'cancelled',
      originalException: originalException,
    );
  }

  /// Creates an AuthException for unknown errors
  factory AuthException.unknown([dynamic originalException]) {
    return AuthException(
      type: AuthErrorType.unknown,
      message: AuthErrorType.unknown.message,
      code: 'unknown',
      originalException: originalException,
    );
  }

  /// Returns whether this error should be retryable
  bool get isRetryable => type.isRetryable;

  /// Returns the error title
  String get title => type.title;

  @override
  String toString() {
    return 'AuthException(type: $type, message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthException &&
        other.type == type &&
        other.message == message &&
        other.code == code;
  }

  @override
  int get hashCode {
    return Object.hash(type, message, code);
  }
}
