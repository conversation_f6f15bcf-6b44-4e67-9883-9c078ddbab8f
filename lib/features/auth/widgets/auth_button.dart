import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Custom button widget for authentication actions
class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final AuthButtonType type;
  final Widget? icon;
  final double? width;
  final double? height;

  const AuthButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.type = AuthButtonType.primary,
    this.icon,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final isButtonEnabled = isEnabled && !isLoading && onPressed != null;

    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? 56.h,
      child: ElevatedButton(
        onPressed: isButtonEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getBackgroundColor(colorScheme),
          foregroundColor: _getForegroundColor(colorScheme),
          disabledBackgroundColor: _getDisabledBackgroundColor(colorScheme),
          disabledForegroundColor: _getDisabledForegroundColor(colorScheme),
          elevation: type == AuthButtonType.primary ? 2 : 0,
          shadowColor: colorScheme.shadow.withOpacity(0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
            side: type == AuthButtonType.outlined
                ? BorderSide(
                    color: isButtonEnabled 
                        ? colorScheme.primary 
                        : colorScheme.outline.withOpacity(0.5),
                    width: 1.5,
                  )
                : BorderSide.none,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: 24.w,
            vertical: 16.h,
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 20.w,
                height: 20.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getForegroundColor(colorScheme),
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (icon != null) ...[
                    icon!,
                    SizedBox(width: 8.w),
                  ],
                  Text(
                    text,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: _getForegroundColor(colorScheme),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Color _getBackgroundColor(ColorScheme colorScheme) {
    switch (type) {
      case AuthButtonType.primary:
        return colorScheme.primary;
      case AuthButtonType.secondary:
        return colorScheme.secondary;
      case AuthButtonType.outlined:
        return Colors.transparent;
      case AuthButtonType.text:
        return Colors.transparent;
    }
  }

  Color _getForegroundColor(ColorScheme colorScheme) {
    switch (type) {
      case AuthButtonType.primary:
        return colorScheme.onPrimary;
      case AuthButtonType.secondary:
        return colorScheme.onSecondary;
      case AuthButtonType.outlined:
        return colorScheme.primary;
      case AuthButtonType.text:
        return colorScheme.primary;
    }
  }

  Color _getDisabledBackgroundColor(ColorScheme colorScheme) {
    switch (type) {
      case AuthButtonType.primary:
      case AuthButtonType.secondary:
        return colorScheme.onSurface.withOpacity(0.12);
      case AuthButtonType.outlined:
      case AuthButtonType.text:
        return Colors.transparent;
    }
  }

  Color _getDisabledForegroundColor(ColorScheme colorScheme) {
    return colorScheme.onSurface.withOpacity(0.38);
  }
}

/// Types of authentication buttons
enum AuthButtonType {
  /// Primary button with filled background
  primary,
  /// Secondary button with filled background
  secondary,
  /// Outlined button with transparent background
  outlined,
  /// Text button with transparent background
  text,
}
