import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Loading indicator widget for authentication operations
class AuthLoadingIndicator extends StatelessWidget {
  final String? message;
  final double? size;
  final Color? color;

  const AuthLoadingIndicator({
    super.key,
    this.message,
    this.size,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size ?? 40.w,
            height: size ?? 40.h,
            child: CircularProgressIndicator(
              strokeWidth: 3.0,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? colorScheme.primary,
              ),
            ),
          ),
          if (message != null) ...[
            SizedBox(height: 16.h),
            Text(
              message!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Full screen loading overlay for authentication operations
class AuthLoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;

  const AuthLoadingOverlay({
    super.key,
    this.message,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      color: Colors.black.withOpacity(0.5),
      child: AuthLoadingIndicator(
        message: message,
        color: Colors.white,
      ),
    );
  }
}

/// Inline loading indicator for buttons and small spaces
class AuthInlineLoadingIndicator extends StatelessWidget {
  final double? size;
  final Color? color;
  final String? text;

  const AuthInlineLoadingIndicator({
    super.key,
    this.size,
    this.color,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size ?? 16.w,
          height: size ?? 16.h,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? colorScheme.primary,
            ),
          ),
        ),
        if (text != null) ...[
          SizedBox(width: 8.w),
          Text(
            text!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: color ?? colorScheme.primary,
            ),
          ),
        ],
      ],
    );
  }
}
