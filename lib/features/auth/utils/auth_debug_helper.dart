import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/providers/auth_providers.dart';

/// Debug helper for testing authentication functionality
class AuthDebugHelper {
  /// Show debug information about current auth state
  static void showAuthDebugInfo(BuildContext context, WidgetRef ref) {
    if (!kDebugMode) return;

    final authStatus = ref.read(authStatusProvider);
    final currentUser = ref.read(currentUserProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    final isLoading = ref.read(isAuthLoadingProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auth Debug Info'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Auth Status: ${authStatus.toString()}'),
            const SizedBox(height: 8),
            Text('Is Authenticated: $isAuthenticated'),
            const SizedBox(height: 8),
            Text('Is Loading: $isLoading'),
            const SizedBox(height: 8),
            Text('Current User: ${currentUser.toString()}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Test signup with debug credentials
  static Future<void> testSignup(WidgetRef ref) async {
    if (!kDebugMode) return;

    try {
      await ref.read(signUpProvider.notifier).signUp(
            email: '<EMAIL>',
            password: 'test123456',
          );
      debugPrint('✅ Test signup completed');
    } catch (e) {
      debugPrint('❌ Test signup failed: $e');
    }
  }

  /// Test login with debug credentials
  static Future<void> testLogin(WidgetRef ref) async {
    if (!kDebugMode) return;

    try {
      await ref.read(signInProvider.notifier).signIn(
            email: '<EMAIL>',
            password: 'test123456',
          );
      debugPrint('✅ Test login completed');
    } catch (e) {
      debugPrint('❌ Test login failed: $e');
    }
  }

  /// Test logout
  static Future<void> testLogout(WidgetRef ref) async {
    if (!kDebugMode) return;

    try {
      await ref.read(signOutProvider.notifier).signOut();
      debugPrint('✅ Test logout completed');
    } catch (e) {
      debugPrint('❌ Test logout failed: $e');
    }
  }

  /// Validate password with debug output
  static void validatePasswordDebug(String password) {
    if (!kDebugMode) return;

    debugPrint('🔍 Validating password: "$password"');
    debugPrint('  - Length: ${password.length}');
    debugPrint('  - Has letters: ${RegExp(r'[a-zA-Z]').hasMatch(password)}');
    debugPrint('  - Has numbers: ${RegExp(r'[0-9]').hasMatch(password)}');
    debugPrint('  - Has special chars: ${RegExp(r'[!@#\$%^&*(),.?":{}|<>]').hasMatch(password)}');
  }

  /// Show debug floating action button for auth testing
  static Widget buildDebugFAB(BuildContext context, WidgetRef ref) {
    if (!kDebugMode) return const SizedBox.shrink();

    return FloatingActionButton(
      onPressed: () => showAuthDebugInfo(context, ref),
      backgroundColor: Colors.red,
      child: const Icon(Icons.bug_report),
    );
  }

  /// Add debug menu to app bar
  static List<Widget> buildDebugAppBarActions(BuildContext context, WidgetRef ref) {
    if (!kDebugMode) return [];

    return [
      PopupMenuButton<String>(
        icon: const Icon(Icons.bug_report, color: Colors.red),
        onSelected: (value) async {
          switch (value) {
            case 'auth_info':
              showAuthDebugInfo(context, ref);
              break;
            case 'test_signup':
              await testSignup(ref);
              break;
            case 'test_login':
              await testLogin(ref);
              break;
            case 'test_logout':
              await testLogout(ref);
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'auth_info',
            child: Text('Show Auth Info'),
          ),
          const PopupMenuItem(
            value: 'test_signup',
            child: Text('Test Signup'),
          ),
          const PopupMenuItem(
            value: 'test_login',
            child: Text('Test Login'),
          ),
          const PopupMenuItem(
            value: 'test_logout',
            child: Text('Test Logout'),
          ),
        ],
      ),
    ];
  }
}
