import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/models/user_model.dart';
import '../../../core/providers/auth_providers.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../utils/auth_debug_helper.dart';
import '../utils/auth_error_handler.dart';
import '../utils/auth_validators.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_error_display.dart';
import '../widgets/auth_text_field.dart';

/// Login screen for user authentication
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateForm);
    _passwordController.addListener(_validateForm);

    // Listen for successful authentication
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen<AsyncValue<UserModel?>>(signInProvider, (previous, next) {
        next.whenOrNull(
          data: (user) {
            if (user != null) {
              // Successful login - show success message
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Welcome back!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
              // Navigation will be handled by the auth state listener in the router
            }
          },
          error: (error, stackTrace) {
            // Error is already handled by the error display widget
            debugPrint('Login error: $error');
          },
        );
      });
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _validateForm() {
    final isValid =
        _emailController.text.trim().isNotEmpty &&
        _passwordController.text.isNotEmpty &&
        AuthValidators.validateEmail(_emailController.text.trim()) == null;

    if (isValid != _isFormValid) {
      setState(() {
        _isFormValid = isValid;
      });
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    try {
      await ref
          .read(signInProvider.notifier)
          .signIn(email: email, password: password);

      // Success is handled by the auth state listener
      AuthErrorHandler.logAuthEvent('login_success', data: {'email': email});
    } catch (error) {
      // Error handling is done by the provider, but we can log it
      AuthErrorHandler.logAuthError('login', error, context: {'email': email});
    }
  }

  void _navigateToSignUp() {
    context.pushNamed('signup');
  }

  void _handleForgotPassword() {
    // TODO: Implement forgot password functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Forgot password functionality coming soon'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ResponsivePage(
      mobile: (context) => Scaffold(
        backgroundColor: colorScheme.surface,
        floatingActionButton: AuthDebugHelper.buildDebugFAB(context, ref),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 60.h),

                  // App Logo/Title
                  _buildHeader(theme),

                  SizedBox(height: 48.h),

                  // Login Form
                  _buildLoginForm(theme),

                  SizedBox(height: 24.h),

                  // Error Display
                  _buildErrorDisplay(),

                  SizedBox(height: 24.h),

                  // Login Button
                  _buildLoginButton(),

                  SizedBox(height: 16.h),

                  // Forgot Password
                  _buildForgotPasswordButton(theme),

                  SizedBox(height: 32.h),

                  // Sign Up Link
                  _buildSignUpLink(theme),

                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        Icon(Symbols.school, size: 64.w, color: theme.colorScheme.primary),
        SizedBox(height: 16.h),
        Text(
          'Welcome Back',
          style: theme.textTheme.headlineMedium?.copyWith(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          'Sign in to continue to Scholara',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm(ThemeData theme) {
    return Column(
      children: [
        AuthTextField(
          label: 'Email',
          hint: 'Enter your email address',
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          isRequired: true,
          prefixIcon: Icon(
            Symbols.email,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          validator: AuthValidators.validateEmail,
          textCapitalization: TextCapitalization.none,
        ),
        SizedBox(height: 20.h),
        AuthTextField(
          label: 'Password',
          hint: 'Enter your password',
          controller: _passwordController,
          isPassword: true,
          isRequired: true,
          prefixIcon: Icon(
            Symbols.lock,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          validator: (value) => AuthValidators.validatePassword(value),
        ),
      ],
    );
  }

  Widget _buildErrorDisplay() {
    return Consumer(
      builder: (context, ref, child) {
        final signInState = ref.watch(signInProvider);

        return signInState.when(
          data: (_) => const SizedBox.shrink(),
          loading: () => const SizedBox.shrink(),
          error: (error, _) => AuthErrorDisplay(
            error: error.toString(),
            onRetry: _handleLogin,
            showRetryButton: true,
          ),
        );
      },
    );
  }

  Widget _buildLoginButton() {
    return Consumer(
      builder: (context, ref, child) {
        final signInState = ref.watch(signInProvider);
        final isLoading = signInState.isLoading;

        return AuthButton(
          text: 'Sign In',
          onPressed: _isFormValid ? _handleLogin : null,
          isLoading: isLoading,
          isEnabled: _isFormValid,
          type: AuthButtonType.primary,
        );
      },
    );
  }

  Widget _buildForgotPasswordButton(ThemeData theme) {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: _handleForgotPassword,
        child: Text(
          'Forgot Password?',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSignUpLink(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        TextButton(
          onPressed: _navigateToSignUp,
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            'Sign Up',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
