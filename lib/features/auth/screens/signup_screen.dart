import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/models/user_model.dart';
import '../../../core/providers/auth_providers.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../utils/auth_debug_helper.dart';
import '../utils/auth_validators.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_error_display.dart';
import '../widgets/auth_text_field.dart';

/// Signup screen for user registration
class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateForm);
    _passwordController.addListener(_validateForm);
    _confirmPasswordController.addListener(_validateForm);

    // Listen for successful authentication
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen<AsyncValue<UserModel?>>(signUpProvider, (previous, next) {
        next.whenOrNull(
          data: (user) {
            if (user != null) {
              // Successful signup - show success message
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Account created successfully! Welcome to Scholara!',
                  ),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
              // Navigation will be handled by the auth state listener in the router
            }
          },
          error: (error, stackTrace) {
            // Error is already handled by the error display widget
            debugPrint('Signup error: $error');
          },
        );
      });
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _validateForm() {
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    final isValid =
        email.isNotEmpty &&
        password.isNotEmpty &&
        confirmPassword.isNotEmpty &&
        AuthValidators.validateEmail(email) == null &&
        AuthValidators.validatePassword(password) == null &&
        AuthValidators.validatePasswordConfirmation(
              confirmPassword,
              password,
            ) ==
            null;

    if (isValid != _isFormValid) {
      setState(() {
        _isFormValid = isValid;
      });
    }
  }

  Future<void> _handleSignUp() async {
    if (!_formKey.currentState!.validate()) {
      // Show validation errors
      setState(() {});
      return;
    }

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    try {
      await ref
          .read(signUpProvider.notifier)
          .signUp(email: email, password: password);

      // Check if signup was successful
      final signUpState = ref.read(signUpProvider);
      signUpState.whenOrNull(
        data: (user) {
          if (user != null) {
            // Success - navigation will be handled by auth state listener
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Account created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
      );
    } catch (error) {
      // Error handling is done by the provider and error display widget
      debugPrint('Signup error: $error');
    }
  }

  void _navigateToLogin() {
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ResponsivePage(
      mobile: (context) => Scaffold(
        backgroundColor: colorScheme.surface,
        floatingActionButton: AuthDebugHelper.buildDebugFAB(context, ref),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Symbols.arrow_back, color: colorScheme.onSurface),
            onPressed: _navigateToLogin,
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 20.h),

                  // Header
                  _buildHeader(theme),

                  SizedBox(height: 40.h),

                  // Signup Form
                  _buildSignupForm(theme),

                  SizedBox(height: 24.h),

                  // Error Display
                  _buildErrorDisplay(),

                  SizedBox(height: 24.h),

                  // Signup Button
                  _buildSignupButton(),

                  SizedBox(height: 32.h),

                  // Login Link
                  _buildLoginLink(theme),

                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        Icon(Symbols.person_add, size: 64.w, color: theme.colorScheme.primary),
        SizedBox(height: 16.h),
        Text(
          'Create Account',
          style: theme.textTheme.headlineMedium?.copyWith(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          'Join Scholara to get started',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSignupForm(ThemeData theme) {
    return Column(
      children: [
        AuthTextField(
          label: 'Email',
          hint: 'Enter your email address',
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          isRequired: true,
          prefixIcon: Icon(
            Symbols.email,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          validator: AuthValidators.validateEmail,
          textCapitalization: TextCapitalization.none,
        ),
        SizedBox(height: 20.h),
        AuthTextField(
          label: 'Password',
          hint: 'Create a strong password',
          controller: _passwordController,
          isPassword: true,
          isRequired: true,
          prefixIcon: Icon(
            Symbols.lock,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          validator: (value) => AuthValidators.validatePassword(value),
        ),
        SizedBox(height: 20.h),
        AuthTextField(
          label: 'Confirm Password',
          hint: 'Re-enter your password',
          controller: _confirmPasswordController,
          isPassword: true,
          isRequired: true,
          prefixIcon: Icon(
            Symbols.lock_reset,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          validator: (value) => AuthValidators.validatePasswordConfirmation(
            value,
            _passwordController.text,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorDisplay() {
    return Consumer(
      builder: (context, ref, child) {
        final signUpState = ref.watch(signUpProvider);

        return signUpState.when(
          data: (_) => const SizedBox.shrink(),
          loading: () => const SizedBox.shrink(),
          error: (error, _) => AuthErrorDisplay(
            error: error.toString(),
            onRetry: _handleSignUp,
            showRetryButton: true,
          ),
        );
      },
    );
  }

  Widget _buildSignupButton() {
    return Consumer(
      builder: (context, ref, child) {
        final signUpState = ref.watch(signUpProvider);
        final isLoading = signUpState.isLoading;

        return AuthButton(
          text: 'Create Account',
          onPressed: _isFormValid ? _handleSignUp : null,
          isLoading: isLoading,
          isEnabled: _isFormValid,
          type: AuthButtonType.primary,
        );
      },
    );
  }

  Widget _buildLoginLink(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        TextButton(
          onPressed: _navigateToLogin,
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            'Sign In',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
