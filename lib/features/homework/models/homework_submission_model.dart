/// Model representing a homework submission by a student
class HomeworkSubmissionModel {
  final String id; // Unique submission ID
  final String homeworkId;
  final String userId;
  final List<String> fileUrls;
  final String? studentNote;
  final DateTime submittedAt;
  final String? teacherRemark;
  final DateTime? reviewedAt;

  const HomeworkSubmissionModel({
    required this.id,
    required this.homeworkId,
    required this.userId,
    required this.fileUrls,
    this.studentNote,
    required this.submittedAt,
    this.teacherRemark,
    this.reviewedAt,
  });

  factory HomeworkSubmissionModel.fromJson(Map<String, dynamic> json) {
    return HomeworkSubmissionModel(
      id: json['id'] as String,
      homeworkId: json['homeworkId'] as String,
      userId: json['userId'] as String,
      fileUrls: List<String>.from(json['fileUrls'] as List),
      studentNote: json['studentNote'] as String?,
      submittedAt: DateTime.parse(json['submittedAt'] as String),
      teacherRemark: json['teacherRemark'] as String?,
      reviewedAt: json['reviewedAt'] != null
          ? DateTime.parse(json['reviewedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'homeworkId': homeworkId,
      'userId': userId,
      'fileUrls': fileUrls,
      'studentNote': studentNote,
      'submittedAt': submittedAt.toIso8601String(),
      'teacherRemark': teacherRemark,
      'reviewedAt': reviewedAt?.toIso8601String(),
    };
  }

  HomeworkSubmissionModel copyWith({
    String? id,
    String? homeworkId,
    String? userId,
    List<String>? fileUrls,
    String? studentNote,
    DateTime? submittedAt,
    String? teacherRemark,
    DateTime? reviewedAt,
  }) {
    return HomeworkSubmissionModel(
      id: id ?? this.id,
      homeworkId: homeworkId ?? this.homeworkId,
      userId: userId ?? this.userId,
      fileUrls: fileUrls ?? this.fileUrls,
      studentNote: studentNote ?? this.studentNote,
      submittedAt: submittedAt ?? this.submittedAt,
      teacherRemark: teacherRemark ?? this.teacherRemark,
      reviewedAt: reviewedAt ?? this.reviewedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomeworkSubmissionModel) return false;
    return other.id == id &&
        other.homeworkId == homeworkId &&
        other.userId == userId &&
        _listEquals(other.fileUrls, fileUrls) &&
        other.studentNote == studentNote &&
        other.submittedAt == submittedAt &&
        other.teacherRemark == teacherRemark &&
        other.reviewedAt == reviewedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      homeworkId,
      userId,
      Object.hashAll(fileUrls),
      studentNote,
      submittedAt,
      teacherRemark,
      reviewedAt,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'HomeworkSubmissionModel(id: $id, homeworkId: $homeworkId, userId: $userId, submittedAt: $submittedAt)';
  }
}
