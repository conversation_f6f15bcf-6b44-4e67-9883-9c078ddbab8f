import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';
import '../../../core/providers/auth_providers.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/enums/homework/homework_status.dart';

import '../controllers/homework_controller.dart';
import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';
import '../../files/services/file_viewer_service.dart';

/// Screen for viewing homework submission details
class ViewSubmissionScreen extends ConsumerWidget {
  /// The homework submission to view
  final HomeworkSubmissionModel? submission;

  /// The homework ID to find the submission for
  final String? homeworkId;

  const ViewSubmissionScreen({super.key, this.submission, this.homeworkId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currentUserId = ref.read(currentUserIdProvider);

    if (submission != null) {
      // Submission provided directly
      return _buildSubmissionView(
        context,
        ref,
        theme,
        colorScheme,
        submission!,
        null,
      );
    } else if (homeworkId != null && currentUserId != null) {
      // Homework ID provided, fetch submission data
      final submissionAsync = ref.watch(
        submissionProvider((homeworkId: homeworkId!, userId: currentUserId)),
      );

      return submissionAsync.when(
        loading: () => Scaffold(
          appBar: AppBar(
            title: Text(
              'Your Submission',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            backgroundColor: colorScheme.surface,
            elevation: 0,
          ),
          body: const Center(child: CircularProgressIndicator()),
        ),
        error: (error, stackTrace) => Scaffold(
          appBar: AppBar(
            title: Text(
              'Your Submission',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            backgroundColor: colorScheme.surface,
            elevation: 0,
          ),
          body: _ErrorState(
            error: error.toString(),
            onRetry: () => ref.invalidate(
              submissionProvider((
                homeworkId: homeworkId!,
                userId: currentUserId,
              )),
            ),
          ),
        ),
        data: (submissionData) {
          if (submissionData == null) {
            return _NoSubmissionState(theme: theme, colorScheme: colorScheme);
          }
          return _buildSubmissionView(
            context,
            ref,
            theme,
            colorScheme,
            submissionData,
            homeworkId,
          );
        },
      );
    } else {
      // Neither submission nor homeworkId provided
      return _NoSubmissionState(theme: theme, colorScheme: colorScheme);
    }
  }

  Widget _buildSubmissionView(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    ColorScheme colorScheme,
    HomeworkSubmissionModel submissionData,
    String? hwId,
  ) {
    // Get homework data
    final homeworkAsync = ref.watch(
      homeworkDetailProvider(submissionData.homeworkId),
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Your Submission',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: homeworkAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => _ErrorState(
          error: error.toString(),
          onRetry: () =>
              ref.invalidate(homeworkDetailProvider(submissionData.homeworkId)),
        ),
        data: (homeworkData) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Homework Overview Section
                      _HomeworkOverviewSection(
                        homework: homeworkData,
                        submission: submissionData,
                      ),

                      SizedBox(height: 24.h),

                      // File List Section
                      _FileListSection(fileUrls: submissionData.fileUrls),

                      SizedBox(height: 24.h),

                      // Note Display Section
                      _NoteDisplaySection(
                        studentNote: submissionData.studentNote,
                      ),

                      SizedBox(height: 24.h),

                      // Teacher's Remark Section
                      if (submissionData.teacherRemark != null)
                        _TeacherRemarkSection(
                          teacherRemark: submissionData.teacherRemark!,
                          reviewedAt: submissionData.reviewedAt,
                        ),

                      if (submissionData.teacherRemark != null)
                        SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),

              // Edit Button
              _EditSubmissionButton(
                homeworkId: submissionData.homeworkId,
                homework: homeworkData,
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Widget to display homework overview information
class _HomeworkOverviewSection extends StatelessWidget {
  final HomeworkModel? homework;
  final HomeworkSubmissionModel submission;

  const _HomeworkOverviewSection({
    required this.homework,
    required this.submission,
  });

  /// Format date and time for display
  String _formatDateTime(DateTime dateTime) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateDay = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (dateDay == today) {
      dateStr = 'Today';
    } else if (dateDay == today.add(const Duration(days: 1))) {
      dateStr = 'Tomorrow';
    } else if (dateDay == today.subtract(const Duration(days: 1))) {
      dateStr = 'Yesterday';
    } else {
      dateStr =
          '${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}';
    }

    final hour = dateTime.hour == 0
        ? 12
        : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';

    return '$dateStr at $hour:$minute $period';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Submission Details',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),

        if (homework != null) ...[
          _InfoRow(label: 'Subject', value: homework!.subject),
          SizedBox(height: 12.h),
          _InfoRow(label: 'Title', value: homework!.title),
          SizedBox(height: 12.h),
          if (homework!.dueAt != null)
            _InfoRow(
              label: 'Due Date',
              value: _formatDateTime(homework!.dueAt!),
            ),
          if (homework!.dueAt != null) SizedBox(height: 12.h),
        ],

        _InfoRow(
          label: 'Submitted At',
          value: _formatDateTime(submission.submittedAt),
        ),
      ],
    );
  }
}

/// Widget to display an information row
class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow({required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100.w,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }
}

/// Widget to display the list of submitted files
class _FileListSection extends StatelessWidget {
  final List<String> fileUrls;

  const _FileListSection({required this.fileUrls});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Submitted Files',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),

        if (fileUrls.isEmpty)
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.3),
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                Icon(
                  Symbols.description,
                  size: 48.sp,
                  color: colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                SizedBox(height: 12.h),
                Text(
                  'No files submitted',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: fileUrls.length,
            separatorBuilder: (context, index) => SizedBox(height: 8.h),
            itemBuilder: (context, index) => _ViewSubmissionFileTile(
              fileUrl: fileUrls[index],
              onTap: () => _handleFileTap(context, fileUrls, index),
            ),
          ),
      ],
    );
  }

  /// Handle file tap - open file in viewer
  void _handleFileTap(
    BuildContext context,
    List<String> fileUrls,
    int currentIndex,
  ) {
    final fileViewerService = FileViewerService();

    // Convert all URLs to FileModel objects
    final fileModels = fileUrls
        .map((url) => fileViewerService.createFileModelFromUrl(url))
        .toList();

    // Open the selected file with navigation support
    fileViewerService.openFile(
      context,
      fileModels[currentIndex],
      fileList: fileModels,
      currentIndex: currentIndex,
    );
  }
}

/// Widget for displaying a submitted file (read-only version of SubmissionFileTile)
class _ViewSubmissionFileTile extends StatelessWidget {
  final String fileUrl;
  final VoidCallback onTap;

  const _ViewSubmissionFileTile({required this.fileUrl, required this.onTap});

  /// Get file extension from URL
  String _getFileExtension(String fileUrl) {
    final fileName = fileUrl.split('/').last;
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  /// Get file name from URL
  String _getFileName(String fileUrl) {
    return fileUrl.split('/').last;
  }

  /// Get appropriate icon for file type
  IconData _getFileIcon(String extension) {
    switch (extension) {
      case 'pdf':
        return Symbols.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Symbols.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Symbols.image;
      case 'mp4':
      case 'mov':
      case 'avi':
        return Symbols.videocam;
      case 'py':
      case 'java':
      case 'js':
      case 'dart':
        return Symbols.code;
      case 'txt':
        return Symbols.text_snippet;
      default:
        return Symbols.attach_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final extension = _getFileExtension(fileUrl);
    final fileName = _getFileName(fileUrl);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Icon(
              _getFileIcon(extension),
              size: 24.sp,
              color: colorScheme.primary,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                fileName,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Symbols.open_in_new,
              size: 20.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget to display the student note section
class _NoteDisplaySection extends StatelessWidget {
  final String? studentNote;

  const _NoteDisplaySection({required this.studentNote});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Submission Note',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),

        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: colorScheme.surface,
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.2),
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            studentNote?.isNotEmpty == true ? studentNote! : 'No note added',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: studentNote?.isNotEmpty == true
                  ? colorScheme.onSurface
                  : colorScheme.onSurface.withValues(alpha: 0.6),
              fontStyle: studentNote?.isNotEmpty == true
                  ? FontStyle.normal
                  : FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }
}

/// Widget to display teacher's remark section
class _TeacherRemarkSection extends StatelessWidget {
  final String teacherRemark;
  final DateTime? reviewedAt;

  const _TeacherRemarkSection({required this.teacherRemark, this.reviewedAt});

  /// Format date and time for display
  String _formatDateTime(DateTime dateTime) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateDay = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (dateDay == today) {
      dateStr = 'Today';
    } else if (dateDay == today.add(const Duration(days: 1))) {
      dateStr = 'Tomorrow';
    } else if (dateDay == today.subtract(const Duration(days: 1))) {
      dateStr = 'Yesterday';
    } else {
      dateStr =
          '${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}';
    }

    final hour = dateTime.hour == 0
        ? 12
        : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';

    return '$dateStr at $hour:$minute $period';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Symbols.rate_review, size: 20.sp, color: colorScheme.primary),
            SizedBox(width: 8.w),
            Text(
              'Teacher\'s Remark',
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        if (reviewedAt != null) ...[
          SizedBox(height: 4.h),
          Text(
            'Reviewed ${_formatDateTime(reviewedAt!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
        SizedBox(height: 12.h),

        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer.withValues(alpha: 0.3),
            border: Border.all(
              color: colorScheme.primary.withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            teacherRemark,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }
}

/// Widget for the edit submission button
class _EditSubmissionButton extends StatelessWidget {
  final String homeworkId;
  final HomeworkModel? homework;

  const _EditSubmissionButton({required this.homeworkId, this.homework});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Check if editing is allowed based on homework status
    final canEdit = homework?.status.canEditSubmission ?? true;
    final isAccepted = homework?.status == HomeworkStatus.accepted;
    final isRejected = homework?.status == HomeworkStatus.rejected;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // Status message for non-editable submissions
          if (!canEdit && !isRejected) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: isAccepted
                    ? Colors.green.withValues(alpha: 0.1)
                    : colorScheme.surfaceContainerHighest,
                border: Border.all(
                  color: isAccepted
                      ? Colors.green.withValues(alpha: 0.3)
                      : colorScheme.outline.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    isAccepted ? Symbols.check_circle : Symbols.info,
                    size: 20.sp,
                    color: isAccepted
                        ? Colors.green
                        : colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      isAccepted
                          ? 'Submission has been accepted and cannot be edited'
                          : 'Submission cannot be edited in current status',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isAccepted
                            ? Colors.green
                            : colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 12.h),
          ],

          // Edit button or status message for rejected submissions
          if (canEdit)
            ElevatedButton(
              onPressed: () => _handleEditSubmission(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: isRejected
                    ? Colors.orange
                    : colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                elevation: 0,
              ),
              child: Text(
                isRejected ? 'Revise Submission' : 'Edit Submission',
                style: theme.textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          else if (!isRejected)
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              decoration: BoxDecoration(
                color: colorScheme.onSurface.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                'Edit Submission',
                textAlign: TextAlign.center,
                style: theme.textTheme.labelLarge?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.38),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Handle edit submission button tap
  void _handleEditSubmission(BuildContext context) {
    // Navigate to submit homework screen with pre-filled data
    context.pushNamed(
      RouteNames.submitHomework,
      pathParameters: {'id': homeworkId},
    );

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening submission editor...'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

/// Error state widget when submission loading fails
class _ErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorState({required this.error, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Symbols.error, size: 64.sp, color: theme.colorScheme.error),
            SizedBox(height: 16.h),
            Text(
              'Failed to load submission',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              error,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Symbols.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}

/// No submission state widget
class _NoSubmissionState extends StatelessWidget {
  final ThemeData theme;
  final ColorScheme colorScheme;

  const _NoSubmissionState({required this.theme, required this.colorScheme});

  @override
  Widget build(BuildContext context) {
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Your Submission',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Symbols.assignment_turned_in,
                size: 64.sp,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              SizedBox(height: 16.h),
              Text(
                'No submission found',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'You haven\'t submitted this homework yet.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.grey[500] : Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
